"""add thumbnail_url to order_items

Revision ID: 4b6a7c487108
Revises: c7620a7b2534
Create Date: 2025-08-25 09:41:21.395089

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4b6a7c487108'
down_revision: Union[str, Sequence[str], None] = 'c7620a7b2534'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('thumbnail_url', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'thumbnail_url')
    # ### end Alembic commands ###
