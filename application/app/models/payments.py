from sqlalchemy import Column, Integer, String, DECIMAL, TIMESTAMP, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from app.models.common import CommonModel
from app.core.constants import PaymentStatus

class PaymentDetails(CommonModel):
    """
    Payment details model - simplified to store one payment mode per record
    """
    __tablename__ = "payment_details"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, index=True)
    payment_order_id = Column(String(50), nullable=True)
    payment_id = Column(String(50), nullable=False, index=True)
    payment_amount = Column(DECIMAL(10, 2), nullable=False)
    payment_date = Column(TIMESTAMP, nullable=False)
    payment_mode = Column(String(50), nullable=False)
    payment_status = Column(Integer, nullable=False, default=PaymentStatus.PENDING, comment="Payment status: 50=Pending, 51=Completed, 52=Failed, 53=Refunded")
    
    # Add the total_amount field that the database requires
    total_amount = Column(DECIMAL(10, 2), nullable=False, default=0.0)

    # Relationship back to order
    order = relationship("Order", backref="payments")


    # Payment mode list
    PAYMENT_MODES = ['cash', 'cod', 'razorpay', 'wallet']

    def __repr__(self):
        return f"<PaymentDetails(id={self.id}, order_id={self.order_id}, payment_id='{self.payment_id}', status={self.payment_status})>"
    
    def get_status_description(self) -> str:
        """Get human-readable description of payment status"""
        return PaymentStatus.get_description(self.payment_status)
    
    def is_completed(self) -> bool:
        """Check if payment is completed"""
        return self.payment_status == PaymentStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """Check if payment failed"""
        return self.payment_status == PaymentStatus.FAILED
    
    def is_pending(self) -> bool:
        """Check if payment is pending"""
        return self.payment_status == PaymentStatus.PENDING
    
    def is_final_status(self) -> bool:
        """Check if payment has reached a final status"""
        return PaymentStatus.is_final_status(self.payment_status)