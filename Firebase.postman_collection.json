{"info": {"_postman_id": "4d3c3ae8-1a40-4bd0-b22d-c61414003bb3", "name": "Firebase", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "45698663"}, "item": [{"name": "app", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "accept-language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"key": "content-type", "value": "application/x-www-form-urlencoded"}, {"key": "origin", "value": "https://web-dev.rozana.tech"}, {"key": "priority", "value": "u=1, i"}, {"key": "referer", "value": "https://web-dev.rozana.tech/"}, {"key": "sec-ch-ua", "value": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""}, {"key": "sec-ch-ua-mobile", "value": "?1"}, {"key": "sec-ch-ua-platform", "value": "\"Android\""}, {"key": "sec-fetch-dest", "value": "empty"}, {"key": "sec-fetch-mode", "value": "cors"}, {"key": "sec-fetch-site", "value": "cross-site"}, {"key": "user-agent", "value": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}, {"key": "x-browser-channel", "value": "stable"}, {"key": "x-browser-copyright", "value": "Copyright 2025 Google LLC. All rights reserved."}, {"key": "x-browser-validation", "value": "Hg4L+ikvx4e+Kz4C1Vi1rALvggw="}, {"key": "x-browser-year", "value": "2025"}, {"key": "x-client-data", "value": "CJO2yQEIo7bJAQipncoBCOrxygEIk6HLAQiko8sBCIagzQEY4eLOARjdgc8B"}, {"key": "x-client-version", "value": "Chrome/JsCore/11.9.1/FirebaseCore-web"}, {"key": "x-firebase-gmpid", "value": "1:211080965257:web:3b917158271e9aa422aa58"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "AMf-vBwuDcyi3Uyhhx1u9QuEJtnnRgcMC3x06S9WRFSVB8R3DLZDP36-ezs01zEa34pg7wdRC6D_1N2-mmdKwx6Hm53te4-yLPBQhwo0cPZPje6IPRj96DEFxcjVP8NhN982COAvQc7YjCNkFIxHgky2rkRxvrGWlX5GDPdAyc43aH6o2OFkgKwyFt08dq1XrJrepQGevu0h27Cg8rZJJsuXtuRGFCHeJeoSn4stYT9BBSfMELG1s5U", "type": "text"}]}, "url": {"raw": "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI", "protocol": "https", "host": ["securetoken", "googlea<PERSON>", "com"], "path": ["v1", "token"], "query": [{"key": "key", "value": "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"}]}}, "response": []}, {"name": "pos", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"wZVWpnSIpY\",\n    \"returnSecureToken\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g", "protocol": "https", "host": ["identitytoolkit", "googlea<PERSON>", "com"], "path": ["v1", "accounts:signInWithPassword"], "query": [{"key": "key", "value": "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g"}]}}, "response": []}]}