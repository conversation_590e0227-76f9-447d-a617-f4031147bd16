{"id": "comprehensive-oms-environment", "name": "Rozana OMS - Environment Variables", "values": [{"key": "base_url", "value": "http://localhost:8000", "enabled": true, "type": "default"}, {"key": "app_firebase_token", "value": "", "enabled": true, "type": "secret", "description": "Firebase token for mobile app authentication. Get this from Firebase App Login endpoint."}, {"key": "pos_firebase_token", "value": "", "enabled": true, "type": "secret", "description": "Firebase token for POS system authentication. Get this from Firebase POS Login endpoint."}, {"key": "api_token", "value": "test_api_token_for_external_integration", "enabled": true, "type": "secret", "description": "API token for external integrations (no Bearer prefix needed)"}, {"key": "customer_id", "value": "test_customer_123", "enabled": true, "type": "default"}, {"key": "facility_id", "value": "test_facility_123", "enabled": true, "type": "default"}, {"key": "facility_name", "value": "Rozana Test Store", "enabled": true, "type": "default"}, {"key": "order_id", "value": "", "enabled": true, "type": "default", "description": "Will be populated after creating an order"}, {"key": "razorpay_order_id", "value": "", "enabled": true, "type": "default", "description": "Will be populated after creating a payment order"}, {"key": "razorpay_payment_id", "value": "", "enabled": true, "type": "default", "description": "Will be populated after payment"}, {"key": "app_firebase_email", "value": "<EMAIL>", "enabled": true, "type": "default", "description": "Email for Firebase app authentication"}, {"key": "app_firebase_password", "value": "your_password", "enabled": true, "type": "secret", "description": "Password for Firebase app authentication"}, {"key": "pos_firebase_email", "value": "<EMAIL>", "enabled": true, "type": "default", "description": "Email for Firebase POS authentication"}, {"key": "pos_firebase_password", "value": "wZVWpnSIpY", "enabled": true, "type": "secret", "description": "Password for Firebase POS authentication"}], "_postman_variable_scope": "environment"}